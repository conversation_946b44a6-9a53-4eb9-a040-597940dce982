﻿
<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - Transport</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <link rel="stylesheet" type="text/css" href="icons.css" />
    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>
    <script src="GeoValidate.js"></script>

    <style>

        .col .row {
            margin-right:auto;
        }

        .Flex-Form {
            overflow:hidden;
        }

        .Flex-Form > div {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .Flex-Form-MainContent {
            border-left: 1px solid transparent;
        }

        .Flex-Form-MainContent > div {
            margin-right: 41px;
            margin-top: 20px;
        }

        .Flex-Form-Diverts {
            display: flex;
            flex-direction: column;
            border-left: 1px solid #DADCE0;
        }

        .Flex-Form-Diverts > .row {
            margin-bottom: 0px;
        }

        #geovalid_address_selected {
            display: inline-block;
            vertical-align: middle;
            margin-top: -4px;
            position: relative;
        }

        #divertLocationRow {
            width: 100%;
            border-bottom: 1px solid #DADCE0;
        }

        #divertLocationName {
            width: calc(-184px + 100%);
            padding: 13px 16px 0px 16px;
            height: 48px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 14px;
            font-weight: bold;
            background-color: #ffc300;
            border-right: 1px solid #7F6100;
        }

        #proceedButton {
            width: 184px;
            height: 48px;
            padding-top: 5px;
            background-color: #ffcf33;
            font-size: 11px !important;
            font-weight: bold;
            padding-left: 16px;
            border-radius: 0px;
            color: unset;
            border: unset;
            box-shadow: unset;
            text-transform: unset;
        }

        #proceedButton > .icon-arrow_forward {
            top:5px;
        }

        #divertChipsRow {
            padding: 16px;
            background-color: #fdfdfe;
            border-bottom: 1px solid #DADCE0;
        }

        #divertChipsRow > .row {
            margin-bottom: 15px;
        }

        #divertChipsRow .chip {
            border: 1px solid #d5d7db;
            background-color: #f5f6f7;
            font-size: 15px;
            height: 24px;
            line-height: 22px;
            color: #151e2e;
            font-weight: 600;
            cursor:pointer;
        }


        #divertListRow {
            overflow: auto;
        }

        #divertListRow > .row {
            margin:0px;
            padding:16px;
            border-bottom: 1px solid #DADCE0;
        }

        #divertListRow > .row > .row:first-child {
            margin-bottom: 8px;
        }

        #divertListRow > .row > .row {
            margin-bottom: 11px;
        }

        #divertListRow > .row > .row:last-child, #divertChipsRow > .row:last-child {
            margin-bottom: 0px;
        }

        #divertListRow .divertHeader, #divertChipsRow > div:first-child {
            font-size: 15px;
            color: #151e2e;
            font-weight: 600;
        }

        #divertListRow .col {
            padding:0px;
        }

        .icon-18 {
            width: 18px;
            height: 18px;
            display: inline-block;
            background-size: contain;
            top:4px;
            position:relative;
        }

        #divertListRow .divertLabel {
            margin-left:10px;
            font-size: 13px;
            color: #192231;
            font-weight: 600;
        }

        #divertListRow .divertValue {
            margin-left: 5px;
            font-size: 13px;
            color: #5f6670;
        }

        #divertListRow .divertComments {
            font-size: 14px;
            color: #5f6670;
        }

        #modal {
            overflow:hidden;
            width:55%;
        }

        #modal .modal-header {
            color: #151e2e;
            font-size: 18px;
        }

        .modal .modal-header.warning {
            background-color: #ffc300;
        }

        #modal .modal-content {
            top: 61px;
            width: 100%;
            height: unset !important;
            overflow-y: auto;
            bottom: 80px;
        }

        #divert_modal_msg {
            color: #192231;
            font-size: 19px;
            margin-top: -3px;
        }

        #divert_modal_reasons {
            color: #5f6670;
            font-size: 16px;
        }

        #divert_modal_reasons .row {
            margin-bottom: 3px;
        }

        #modal .modal-footer {
            height:80px;
            padding: 16px;
        }

        #modal .modal-footer > a {
            margin: 0px !important;
            text-align: center;
            line-height: 46px;
            height: 48px;
            min-width: 150px;
            margin-left: 8px !important;
            padding-left: 20px !important;
            padding-right: 20px !important;
        }

        #modal .modal-footer > a.btn {

            line-height: 48px;
        }



    </style>

    <script language="javascript">
        $(document).ready(function () {

            // Clear cached location and address data to prevent form caching
            clearFormCache();

            /* Hospitals Search */
            $("#hospitalsTextSearch").on("input", GetTableDataForHospitals);
            $("#hospitalsTextSearch").on("focus", function () {
                $("#icon-search-hospitals").addClass("icon-search-blue");
            });
            $("#hospitalsTextSearch").on("focusout", function () {
                $("#icon-search-hospitals").removeClass("icon-search-blue");
            });

            /* Location Search */
            $("#locationsTextSearch").on("input", GetTableData);
            $("#locationsTextSearch").on("focus", function () {
                $("#icon-search").addClass("icon-search-blue");
            });
            $("#locationsTextSearch").on("focusout", function () {
                $("#icon-search").removeClass("icon-search-blue");
            });

            // default warning to NotWarned in xamarin
            if (!CefSharpEnabled) {
                $("#transportationwarning").val("NotWarned");
            }

            // Clicking procced is the same as submit
            $("#proceedButton").click(function () {
                $("#Form").submit();
            });

            //Set up Tab control
            $('.tabs').tabs();

            //handle form submition
            $("#Form").submit(function () {
                if (ValidateLocationFormsWithHospitals() && $(this)[0].checkValidity() == true) {

                    // Show confim if needed
                    ShowConfirm($(this));
                }

                ValidateRequiredSingleSelectOnly($("#transportationprotocol"));
                ValidateRequiredSingleSelectOnly($("#transportationpriority"));

                return false;
            });

            //focus on registration id field
            $("#currentodometer").focus();

        });

        function decimalCheck(input, event) {
            var str = input.value;
            if (event.keyCode == 69) {
                return false;
            }
            else if (str.includes(".") && !isNaN(event.key)) {
                var index = str.indexOf(".");
                if (str.length - index > 1) {
                    return false;
                }
            }
            return true;
        }

        function lengthCheck(input) {
            if (input.value.includes(".")) {
                if (input.value.length > input.maxLength + 2) {
                    input.value = input.value.slice(0, input.maxLength);
                }
            }
            else {
                if (input.value.length > input.maxLength) {
                    input.value = input.value.slice(0, input.maxLength);
                }
            }
            return input;
        }

        function autoSelectSingleOption($select, selectName) {
            // Use multiple attempts with increasing delays to ensure the select is fully loaded
            var attempts = 0;
            var maxAttempts = 10;

            function tryAutoSelect() {
                attempts++;
                var options = $select.find("option");

                console.log(selectName + " Auto-select attempt " + attempts + ":");
                console.log("Total options found:", options.length);

                if (options.length === 0 && attempts < maxAttempts) {
                    // Options not loaded yet, try again
                    setTimeout(tryAutoSelect, 200 * attempts);
                    return;
                }

                options.each(function(index) {
                    console.log("Option " + index + ":", "value='" + $(this).val() + "'", "text='" + $(this).text() + "'");
                });

                var nonEmptyOptions = options.filter(function() {
                    var val = $(this).val();
                    return val !== "" && val !== null && val !== undefined && val.trim() !== "";
                });

                console.log("Non-empty options found:", nonEmptyOptions.length);

                if (nonEmptyOptions.length === 1) {
                    console.log("Auto-selecting single " + selectName + " option:", nonEmptyOptions.first().val());

                    // Use the same pattern as other successful auto-selections in the codebase
                    nonEmptyOptions.first().attr('selected', 'selected');
                    nonEmptyOptions.first().prop("selected", true);

                    // Force update the select display using both methods for compatibility
                    if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
                        $select.formSelect();
                    }
                    else {
                        $select.material_select();
                    }

                    // Additional force update
                    $select.trigger('change');
                    console.log(selectName + " auto-selection completed");
                } else if (attempts < maxAttempts) {
                    // Try again if we haven't reached max attempts
                    setTimeout(tryAutoSelect, 200 * attempts);
                }
            }

            // Start the first attempt immediately
            tryAutoSelect();
        }

        function clearFormCache() {
            // Clear the global paramObjs that stores cached form parameters
            if (typeof paramObjs !== 'undefined') {
                paramObjs = [];
                console.log("Cleared paramObjs cache");
            }

            // Clear the hidden location field that gets cached
            $("#location").val("");

            // Clear address fields that might be cached
            $("#Address").val("");
            $("#City").val("");
            $("#Zip").val("");
            $("#transportationapartment").val("");
            $("#transportationbuilding").val("");

            // Clear the selected address tab and index - set to hospitals tab as default
            $("#SelectedAddressTab").val("hospitalstab");
            $("#SelectedAddressIndex").val("-1");

            // Clear any location table selections
            $("#locationbody").empty();
            $("#verifiedLocationsList").empty();

            // Clear any cached form data
            $("#Form").removeData("lastQueriedLocation");
            $("#Form").removeData("selectedTabForLastQuery");

            // Clear diverts data from tabs
            $("#hospitalstab").removeData("diverts");
            $("#locationtab").removeData("diverts");
            $("#addresstab").removeData("diverts");

            // Force clear all form inputs
            $("#Form input[type='text']").not("#currentodometer, #PatientsTransported, #PatientsSeen, #Comments").val("");
            $("#Form input[type='hidden']").not("#transportationwarning, #CallTaking_Performed_By").val("");

            console.log("Form cache cleared - location and address data reset");
        }

        // Override SetSelectBoxFromParameters for location/address fields to prevent caching
        function SetSelectBoxFromParametersNoCaching($select) {
            // Only update the Materialize display, don't restore cached values
            if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
                $select.formSelect();
            }
            else {
                $select.material_select();
            }
        }

        function clearFormCacheAggressively() {
            console.log("Running aggressive form cache clear...");

            var currentTab = GetCurrentTab();

            // Clear location data (but not if we're on location tab)
            if (currentTab != "locationtab") {
                $("#location").val("");
                $("#locationbody").empty();
                $("#locationsTextSearch").val("");
            }

            // Clear hospitals data (but preserve if we're on hospitals tab with search results)
            if (currentTab != "hospitalstab") {
                $("#hospitalsbody").empty();
                $("#hospitalsTextSearch").val("");
            } else {
                // If we're on hospitals tab, preserve the "hospital" search and results
                console.log("Preserving hospitals search results - current tab is hospitals");
            }

            // Clear address data (but not if we're on address tab)
            if (currentTab != "addresstab") {
                $("#Address").val("");
                $("#City").val("");
                $("#Zip").val("");
                $("#transportationapartment").val("");
                $("#transportationbuilding").val("");
                $("#verifiedLocationsList").empty();

                // Clear state dropdown selection
                $("#transportstate").val("").trigger('change');
                if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
                    $("#transportstate").formSelect();
                } else {
                    $("#transportstate").material_select();
                }
            }

            // Reset tab selection to hospitals tab
            $("#SelectedAddressTab").val("hospitalstab");
            $("#SelectedAddressIndex").val("-1");

            // Clear any visual selections (but not from the current active tab)
            if (currentTab == "hospitalstab") {
                $("#locationtab .selected").removeClass("selected");
                $("#addresstab .selected").removeClass("selected");
            } else if (currentTab == "locationtab") {
                $("#hospitalstab .selected").removeClass("selected");
                $("#addresstab .selected").removeClass("selected");
            } else {
                $("#hospitalstab .selected").removeClass("selected");
                $("#locationtab .selected").removeClass("selected");
            }
            $("#geovalid_address_selected").hide();

            console.log("Aggressive cache clear completed - preserved", currentTab, "data");
        }

        function AfterFillForm() {

            GenerateSelectBox("transportationprotocol", "transportprotocol.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                $("#protocolvals").prepend(result);
                SetSelectBoxFromParameters($("#transportationprotocol"));

                // Immediate attempt to auto-select using the pattern from other files
                var $protocolSelect = $("#transportationprotocol");
                var $firstNonEmptyOption = $protocolSelect.find("option").filter(function() {
                    var val = $(this).val();
                    return val !== "" && val !== null && val !== undefined && val.trim() !== "";
                }).first();

                if ($firstNonEmptyOption.length > 0) {
                    $firstNonEmptyOption.attr('selected', 'selected');
                    $firstNonEmptyOption.prop("selected", true);
                    if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
                        $protocolSelect.formSelect();
                    } else {
                        $protocolSelect.material_select();
                    }
                }

                // Backup auto-select with retry logic
                autoSelectSingleOption($protocolSelect, "Transport Protocol");

                $("#transportationprotocol").prop("required", true);
                $("#transportationprotocol").change(function () {
                    ValidateRequiredSingleSelectOnly($(this));

                    // Reload divert panel when changing protocol
                    LoadDiverts();
                });

                // Load diverts on initial load when diverts are passed in. Wait till this time so that the protocol has been set and the list is filtered correctly
                LoadDiverts();

                GenerateSelectBox("transportationpriority", "transportpriority.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                    $("#priorityvals").prepend(result);
                    SetSelectBoxFromParameters($("#transportationpriority"));

                    // Immediate attempt to auto-select using the pattern from other files
                    var $prioritySelect = $("#transportationpriority");
                    var $firstNonEmptyOption = $prioritySelect.find("option").filter(function() {
                        var val = $(this).val();
                        return val !== "" && val !== null && val !== undefined && val.trim() !== "";
                    }).first();

                    if ($firstNonEmptyOption.length > 0) {
                        $firstNonEmptyOption.attr('selected', 'selected');
                        $firstNonEmptyOption.prop("selected", true);
                        if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
                            $prioritySelect.formSelect();
                        } else {
                            $prioritySelect.material_select();
                        }
                    }

                    // Backup auto-select with retry logic
                    autoSelectSingleOption($prioritySelect, "Transport Priority");

                    $("#transportationpriority").prop("required", true);
                    $("#transportationpriority").change(function () {
                        ValidateRequiredSingleSelectOnly($(this));
                    });

                    GenerateSelectBox("gender", "Gender.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                        $("#gendervals").prepend(result);
                        SetSelectBoxFromParameters($("#gender"));
                        GenerateSelectBox("transportstate", "state.xml", "genericselectvalue.xsl", false, false, false, 1, false, false).then(function (result) {
                            $("#statevals").prepend(result);
                            SetSelectBoxFromParametersNoCaching($("#transportstate")); // Use no-caching version for address-related field
                            setVerifiedAddress();
                            AfterAllControlsInitialized(); // must go after last select intialized

                            // Final aggressive clear after all controls are initialized
                            setTimeout(function() {
                                clearFormCacheAggressively();
                            }, 1000);

                            //GenerateSelectBox("Race", "Race.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                            //    $("#racevals").prepend(result);
                            //    SetSelectBoxFromParameters($("#Race"));
                            //});
                        });
                    });
                });
            });

            // initialze modal
            if (!CefSharpEnabled)//xamarin only
            {
                $('.modal').modal();
            }

            // Override setDefaultLocation to prevent caching - we want a clean form
            // setDefaultLocation(); // Commented out to prevent location caching

            // Ensure hospitals tab is selected and focused
            $("#SelectedAddressTab").val("hospitalstab");
            SetCurrentTab();

            // Additional delay to ensure tab is properly set after all initialization
            setTimeout(function() {
                $("#SelectedAddressTab").val("hospitalstab");
                $("#tab0").children()[0].click();
                console.log("Hospitals tab forced to focus");

                // Auto-search for "hospital" in the hospitals tab
                setTimeout(function() {
                    $("#hospitalsTextSearch").val("hospital");
                    $("#hospitalsTextSearch").trigger("input");
                    console.log("Auto-searched for 'hospital' in hospitals tab");
                }, 200);
            }, 100);
        }

        function SetCurrentTab() {
            var selectedAddressTab = $("#SelectedAddressTab").val();
            console.log("SetCurrentTab called with selectedAddressTab:", selectedAddressTab);

            if (selectedAddressTab == "hospitalstab") {
                console.log("Setting hospitals tab as active");
                $("#tab0").children()[0].click();
            }
            else if (selectedAddressTab == "locationtab") {
                console.log("Setting location tab as active");
                $("#tab1").children()[0].click();
            }
            else {
                console.log("Setting address tab as active (default)");
                $("#tab2").children()[0].click();
                $("#SelectedAddressTab").val(selectedAddressTab);
            }
        }

        function GetCurrentTab() {

            var selectedAddressTab = $("#SelectedAddressTab").val();
            if (selectedAddressTab == "hospitalstab") {
                return "hospitalstab";
            }
            else if (selectedAddressTab == "locationtab") {
                return "locationtab";
            }
            else {
                return "addresstab";
            }
        }

        // Function to switch between table and button view for hospitals
        function SwitchHospitalView() {
            // Hide the original table-based hospitals tab
            $("#hospitalstab").hide();
            // Show the button-based hospitals tab
            $("#hospitalstab-buttons").show();

            // Copy search functionality to the button version
            $("#hospitalsTextSearchButtons").attr("tbody", "hospitalsbody-buttons");
            $("#hospitalsTextSearchButtons").off('input').on('input', GetTableDataForHospitals);
        }

        // Function to initialize the hospital button view
        function InitializeHospitalButtons() {
            // Switch to button view
            SwitchHospitalView();

            // Auto-search for "hospital" in the buttons version
            setTimeout(function() {
                $("#hospitalsTextSearchButtons").val("hospital");
                $("#hospitalsTextSearchButtons").trigger("input");
                console.log("Auto-searched for 'hospital' in hospitals buttons");
            }, 300);
        }

        function GetFacilityDiverts(locationName) {
            // request facility divert for location
            if (!CefSharpEnabled) { // Xamarin only
                var param = {
                    _method: 'getFacilityDiverts',
                    _locationName: locationName
                }
                invokeCSharp(JSON.stringify(param));
            }
        }

        // called by c# whenthe facility divert request returns
        function FacilityDivertReturn(diverts) {

            if (diverts) {

                var response = JSON.parse(diverts);
                var currentTab = GetCurrentTab();

                // if the user has selected another location or changed the tab since this facility divert status was requested just throw away the response
                if (response.locationName == $("#Form").data("lastQueriedLocation") && currentTab == $("#Form").data("selectedTabForLastQuery")) {

                    LoadDiverts(response.diverts);
                }
            }
        }

        // call by c# when loading the transport form from a status error with divert information already contained
        function SetDiverts(diverts) {

            diverts = JSON.parse(diverts);

            var currentTab = GetCurrentTab();
            $("#" + currentTab).data("diverts", diverts);
        }

        function LoadDiverts(diverts) {

            if (!diverts) {
                diverts = $("#" + GetCurrentTab()).data("diverts")
            }

            // clear diver panel if there are no diverts
            if (diverts && diverts.length > 0) {
                BuildDiverts(diverts);
            }
            else {
                ClearDiverts();
            }
        }

        function BuildDiverts(diverts) {

            // cache diverts to current tab
            var currentTab = GetCurrentTab();
            $("#" + currentTab).data("diverts", diverts);

            // clear DOM
            $("#divertListRow,#divertChipList,#divert_modal_reasons").empty();

            var allReasons = [];
            var visibleDiverts = 0;
            var currentProtocol = $("#protocolvals .dropdown-trigger").val();

            // process all diverts
            for (var i = 0; i < diverts.length; i++) {
                var divert = diverts[i];

                // find if current divert matches selected protocol
                var divertReasons = "";
                for (var j = 0; j < divert.Reason.length; j++) {

                    var reason = divert.Reason[j];
                    if (!currentProtocol || !reason.Protocols || reason.Protocols.split(",").includes(currentProtocol)) {

                        divertReasons += reason.Name + " \u2014 "; // add dash character
                        if (!allReasons.includes(reason.Name)) {
                            allReasons.push(reason.Name); // keep list of unique reasons so we dont duplicate chips also used by modal

                            // Add chip for each reason
                            var $chip = $('<div class="chip">' + reason.Name + '</div>');
                            $chip.click((e) => {

                                var $e = $(e.currentTarget);
                                $("#divertListRow div:contains(" + $e.text() + ")")[0].scrollIntoView();
                            })

                            $("#divertChipList").append($chip);
                        }
                    }
                }

                // Build card for divert if it has matching reasons
                if (divertReasons) {

                    visibleDiverts++;
                    $("#divertLocationName").text(divert.LocationName.toUpperCase());
                    divertReasons = divertReasons.substring(0, divertReasons.length - 3);

                    if (!divert.EndDate) {
                        divert.EndDate = "N/A";
                    }

                    var $divertRow = $('<div class="row"></div>');

                    $divertRow.append('<div class="row divertHeader">' + divertReasons + '<div>');

                    var $timeRow = $('<div class="row"></div>');
                    $timeRow.append('<div class="col s6"><i class="icon-clock icon-18"></i><span class="divertLabel">Starts:</span><span class="divertValue">' + divert.StartDate + '</span></div>');
                    $timeRow.append('<div class="col s6"><span class="divertLabel">Ends:</span><span class="divertValue">' + divert.EndDate + '</span></div>');
                    $divertRow.append($timeRow);

                    if (divert.Comments) {
                        $divertRow.append('<div class="row divertComments">' + divert.Comments + '<div>');
                    }

                    $("#divertListRow").append($divertRow);
                }
            }

            // if we have divert that matches selected protocol show divert panel
            if (visibleDiverts > 0) {

                // Show chips
                if (visibleDiverts > 1) {
                    $("#divertChipsRow").show();
                }
                else {
                    $("#divertChipsRow").hide();
                }

                // Update modal
                $("#divert_modal_msg").text("You will procced to " + divert.LocationName + ", which has the follow diverts:");
                for (var k = 0; k < allReasons.length; k++) {

                    if (k % 2 == 0) {
                        var $modalRow = $('<div class="row"></div>');
                        $("#divert_modal_reasons").append($modalRow);
                    }

                    $modalRow.append('<div class="col s6">- ' + allReasons[k] + '</div>');
                }

                ShowDivertPanel();
            }
            else {
                HideDivertPanel();
            }
        }

        function ClearDiverts() {

            // clear cached diverts
            var currentTab = GetCurrentTab();
            $("#" + currentTab).removeData("diverts");
            HideDivertPanel();
        }

        function ShowDivertPanel() {

            // Update html to not shrink when divert panel is shown
            $(".Flex-Form-Diverts").show();
            $(".Flex-Form-MainContent > div").css("margin-right", "20px");
            $(".Flex-Form-MainContent > .col > .row > .s2").removeClass("s2").addClass("s4");
            $(".Flex-Form-MainContent > .col > .row > .s6").removeClass("s6").addClass("s12");
            $(".Flex-Form-MainContent > .col > .row > .row .s8").removeClass("s8").addClass("s12");
            $(".Flex-Form-MainContent .tab.s2").removeClass("s2").addClass("s4");
            $("#addresstab .input-field.s2").removeClass("s2").addClass("s3");
            $("#addresstab .input-field.s4").removeClass("s4").addClass("s6");
            $('.tabs').tabs();
        }

        function HideDivertPanel() {

            // Update html to not grow when divert panel is hidden
            $(".Flex-Form-Diverts").hide();
            $(".Flex-Form-MainContent > div").css("margin-right", "41px");
            $(".Flex-Form-MainContent > .col > .row > .s4").removeClass("s4").addClass("s2");
            $(".Flex-Form-MainContent > .col > .row > .s12").removeClass("s12").addClass("s6");
            $(".Flex-Form-MainContent > .col > .row > .row .s12").removeClass("s12").addClass("s8");
            $(".Flex-Form-MainContent .tab.s4").removeClass("s4").addClass("s2");
            $("#addresstab .input-field.s3").removeClass("s3").addClass("s2");
            $("#addresstab .input-field.s6").removeClass("s6").addClass("s4");
            $('.tabs').tabs();
        }

        function LocationSelected(value) {

            if (value) {
                // if a different location is selected rerun query
                if (value != $("#Form").data("lastQueriedLocation")) {
                    GetFacilityDiverts(value);
                    $("#Form").data("selectedTabForLastQuery", GetCurrentTab());

                    // always clear diverts when requesting diverts for a new location
                    ClearDiverts();
                }
            }

            // if selecting an address without a value we still want to invalidate previous queries
            $("#Form").data("lastQueriedLocation", value);
        }

        function LocationCleared() {

            // clear last queried location when clearing location so that outstanding divert request are ignored
            $("#Form").data("lastQueriedLocation", "");
            ClearDiverts();
        }

        // Custom location click handler that works for both hospitals (buttons) and location tabs (table rows)
        function HospitalLocationItemClicked() {
            var value = $(this).data("value");
            if (value != undefined) {
                // Clear selections from both hospitals and location tabs
                $("#hospitalstab").find("tr").removeClass("selected");
                $("#locationtab").find("tr").removeClass("selected");
                $("#hospitalsbody-buttons").find("button").removeClass("selected");
                $(this).addClass("selected");
                $("#location").val(value);
                $(".table-validate-required").hide();

                if (typeof (LocationSelected) == typeof (Function)) {
                    // notify form that location has been selected
                    LocationSelected(value);
                }
            }
        }

        // Custom GetTableData function for hospitals tab that uses the custom click handler
        function GetTableDataForHospitals() {
            var getTableDataTimeOut;
            clearTimeout(getTableDataTimeOut);
            var $input = $(this);
            var searchText = $input.val();
            var source = $input.attr("source");
            var tbody = $input.attr("tbody");
            var searchField = $input.attr("searchField");
            var $table = $("#" + tbody);

            if (typeof (LocationCleared) == typeof (Function)) {
                // notify form that location has been cleared
                LocationCleared();
            }

            if (searchText != "" && searchText.length > 1) {
                getTableDataTimeOut = setTimeout(function () {
                    if (CefSharpEnabled == true) { //WPF Implementation
                        ClientJsObject.getDataByFilter(source, searchText, "", "", false, false).then(function (ret) {
                            var items = JSON.parse(ret);
                            $table.empty();
                            $("#location").val("");
                            if (items != null && items.length > 0) {
                                for (var i = 0; i < items.length; i++) {
                                    var value = items[i];
                                    $row = $("<tr><td>" + value + "</td></tr>");
                                    $row.data("value", value);
                                    $row.data("index", i);
                                    $row.click(HospitalLocationItemClicked); // Use custom click handler
                                    $table.append($row);
                                }
                            }
                        });
                    }
                    else {
                        var param = {
                            _method: 'getDataByFilter',
                            _callback: 'GetDataByFilter_callback_Hospitals',
                            _source: source,
                            _filter: searchText,
                            _searchField: searchField,
                            _tbody: tbody
                        }
                        GetDataByFilter_callback_Hospitals.df = $.Deferred();
                        GetDataByFilter_callback_Hospitals.parameters = param;
                        invokeCSharp(JSON.stringify(param));
                        $.when(GetDataByFilter_callback_Hospitals.df).done(function (result) {
                            if (result != "") {
                                var items = JSON.parse(result);
                                $table.empty();
                                $("#location").val("");
                                if (items != null && items.length > 0) {
                                    for (var i = 0; i < items.length; i++) {
                                        var value = items[i];
                                        $row = $("<tr><td>" + value + "</td></tr>");
                                        $row.data("value", value);
                                        $row.data("index", i);
                                        $row.click(HospitalLocationItemClicked); // Use custom click handler
                                        $table.append($row);
                                    }
                                }
                            }
                        });
                    }
                }, 300);
            }
            else {
                //clear tbody
                $table.empty();
            }
        }

        // Callback function for hospitals search in Xamarin environment
        function GetDataByFilter_callback_Hospitals(result) {
            if (result == '__fail__') {
                GetDataByFilter_callback_Hospitals.df.fail(result);
            }
            else {
                GetDataByFilter_callback_Hospitals.df.resolve(result);
            }
        }

        // Custom validation function that treats hospitals tab the same as location tab
        function ValidateLocationFormsWithHospitals() {
            var currentTab = GetCurrentTab();

            // If we're on the hospitals tab, temporarily change the selected tab to locationtab
            // for validation purposes, then change it back
            if (currentTab == "hospitalstab") {
                var originalTab = $("#SelectedAddressTab").val();
                $("#SelectedAddressTab").val("locationtab");

                var result = ValidateLocationForms();

                // Change it back to hospitals tab
                $("#SelectedAddressTab").val(originalTab);

                return result;
            }
            else {
                // For location and address tabs, use normal validation
                return ValidateLocationForms();
            }
        }

        function AfterTabChanged(value) {

            // each tab can have different cached set of diverts for the location selected under that address.
            // reload panel after changing tab
            LoadDiverts();
        }

        async function ShowConfirm($form) {

            // if divert panel is visible show modal for confirmation
            var allowSubmit = true;
            if ($(".Flex-Form-Diverts").is(":visible")) {
                allowSubmit = await openModal('PROCEED TO LOCATION?');
                if (allowSubmit) {
                    // if the select confim then updat this flag to allow the server to bypass validation of divert status
                    $("#transportationwarning").val("Warned");
                }
            }

            if (allowSubmit) {

                $(':disabled').each(function (e) {
                    $(this).removeAttr('disabled');
                })

                // If we are submitting with the address tab selected dont submit a location.
                // This can cause a bug were the wrong location is passed to the business layer
                var currentTab = GetCurrentTab();
                if (currentTab != "locationtab" && currentTab != "hospitalstab") {
                    $("#location").val("");
                }

                var values = $form.serialize();
                SubmitQuery(values, $form.attr('action'));
            }
        }

    </script>

</head>
<body class="FlexBody">
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">TRANSPORT</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="Flex-Form" action="TransportQuery.aspx?queryfile=person.qry" method="post" id="Form" name="Form">
        <div class="Flex-Form-MainContent">
            <div class="col m8" style="margin-top:20px">
                <!-- First row: Current Odometer, Number Transported, Gender (equal width totaling s8) -->
                <div class="row">
                    <div class="input-field col s3">
                        <input name="currentodometer" id="currentodometer" type="number" step=".1" maxlength="9" placeholder=""
                               oninput="return lengthCheck(this)"
                               onkeydown="return decimalCheck(this, event)" required>
                        <label for="currentodometer" class="active">Current Odometer</label>
                    </div>
                    <div class="input-field col s3">
                        <input name="PatientsTransported" id="PatientsTransported" type="number" value="1" min="1" maxlength="10" placeholder="" class="input-validate-transported"
                               oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                        <label for="PatientsTransported" class="active">Number Transported</label>
                    </div>
                    <div class="input-field col s2" type="selectlist" id="gendervals" name="gendervals">
                        <label for="gender">Gender</label>
                    </div>
                </div>

                <!-- Second row: Comments spanning the same width as Address Tab -->
                <div class="row">
                    <div class="input-field col s8">
                        <textarea id="Comments" placeholder="" style="height: 3rem !important" class="materialize-textarea" name="Comments"></textarea>
                        <label for="Comments" class="active">Comments</label>
                    </div>
                </div>

                <!-- Hidden fields that are auto-defaulted -->
                <div style="display: none;">
                    <div class="input-field col s2" type="selectlist" id="protocolvals" name="protocolvals">
                        <label for="transportationprotocol">Transport Protocol</label>
                    </div>
                    <div class="input-field col s2" type="selectlist" id="priorityvals" name="priorityvals">
                        <label for="transportationpriority">Transport Priority</label>
                    </div>
                    <div class="input-field col s2">
                        <input name="PatientsSeen" id="PatientsSeen" type="number" value="1" min="1" maxlength="9" placeholder=""
                               oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                        <label for="PatientsSeen" class="active">Number Seen</label>
                    </div>
                </div>
                <div class="row">
                    <label class="col s6">
                        <input type="checkbox" name="isjuvenile" id="isjuvenile" class="filled-in checkbox-color" />
                        <span>Is Juvenile</span>
                    </label>
                </div>
                <!--use last row on bottom last-row-->
                <div class="row last-row">
                    <ul class="tabs" id="radio_tabs" style="margin-bottom: 30px; margin-left: 10px;">
                        <li class="tab col s2" id="tab0"><a href="#hospitalstab" onclick="setTab('hospitalstab')">Hospitals</a></li>
                        <li class="tab col s2" id="tab1"><a href="#locationtab" onclick="setTab('locationtab')">Location</a></li>
                        <li class="tab col s2" id="tab2"><a href="#addresstab" onclick="setTab('addresstab')">Address</a></li>
                    </ul>

                    <!--Hospitals-->
                    <div class="row" id="hospitalstab">
                        <div class="row">
                            <div class="responsive-table col s8" style="margin-top: 20px;">
                                <table class="tableSearch highlight">
                                    <thead>
                                        <tr>
                                            <th class="center" style="padding:0">
                                                <div class="row">
                                                    <div class="input-field col">
                                                        <i class="icon-search icon-20" id="icon-search-hospitals"></i>
                                                    </div>
                                                    <div class="input-field col">

                                                        <input class="searchStyle" placeholder="Search Hospitals"
                                                               id="hospitalsTextSearch" type="text"
                                                               source="TransportLocation.xml" tbody="hospitalsbody" searchField="description">
                                                    </div>
                                                </div>

                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="hospitalsbody"></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="row">
                            <label class="table-validate-required col s8" style="display:none">Field is Required</label>
                        </div>
                    </div>
                    <!--Hospital Buttons Container-->
                    <div class="row" id="hospitalstab-buttons" style="display:none;">
                        <div class="row">
                            <div class="col s8" style="margin-top: 20px;">
                                <div class="hospital-search-container">
                                    <div class="row" style="margin-bottom: 10px;">
                                        <div class="input-field col s12">
                                            <i class="icon-search icon-20" id="icon-search-hospitals-buttons" style="position: absolute; left: 10px; top: 12px;"></i>
                                            <input class="searchStyle" placeholder="Search Hospitals"
                                                   id="hospitalsTextSearchButtons" type="text"
                                                   source="TransportLocation.xml" tbody="hospitalsbody-buttons" searchField="description"
                                                   style="padding-left: 35px;">
                                        </div>
                                    </div>
                                </div>
                                <div id="hospitalsbody-buttons" class="hospital-buttons-container" style="max-height: 150px; overflow-y: auto; border: thin solid black; background-color: white; padding: 10px;">
                                    <!-- Hospital buttons will be populated here -->
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <label class="table-validate-required col s8" style="display:none">Field is Required</label>
                        </div>
                    </div>
                    <!--Location-->
                    <div class="row" id="locationtab">
                        <div class="row">
                            <div class="responsive-table col s8" style="margin-top: 20px;">
                                <table class="tableSearch highlight">
                                    <thead>
                                        <tr>
                                            <th class="center" style="padding:0">
                                                <div class="row">
                                                    <div class="input-field col">
                                                        <i class="icon-search icon-20" id="icon-search"></i>
                                                    </div>
                                                    <div class="input-field col">

                                                        <input class="searchStyle" placeholder="Search Locations"
                                                               id="locationsTextSearch" type="text"
                                                               source="TransportLocation.xml" tbody="locationbody" searchField="description">
                                                    </div>
                                                </div>

                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="locationbody"></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="row">
                            <label class="table-validate-required col s8" style="display:none">Field is Required</label>
                        </div>
                    </div>
                    <div class="row" id="addresstab">
                        <div class="row">
                            <div class="input-field col s4" mandatory="true">
                                <input placeholder="" id="Address" name="Address" type="text" maxlength="400" oninput="GeoButtonVisibility()">
                                <label for="Address" class="active">Address</label>
                            </div>
                            <div class="input-field col s2" mandatory="true">
                                <input placeholder="" id="transportationapartment" name="transportationapartment" type="text" maxlength="10">
                                <label for="transportationapartment" class="active">Room/Apt</label>
                            </div>
                            <div class="input-field col s2" mandatory="true">
                                <input placeholder="" id="transportationbuilding" name="transportationbuilding" type="text" maxlength="10">
                                <label for="transportationbuilding" class="active">Building</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s4" mandatory="true">
                                <input placeholder="" id="City" name="City" type="text" maxlength="35">
                                <label for="City" class="active">City</label>
                            </div>
                            <div class="input-field col s2" type="selectlist" id="statevals" name="statevals">
                                <label>State</label>
                            </div>
                            <div class="input-field col s2" mandatory="true">
                                <input placeholder="" id="Zip" name="Zip" type="text" maxlength="10">
                                <label for="Zip" class="active">Zip</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col s6">
                                <button class="mdc-button btn Geo_Validate_Address disabled col s4" type="button" style="min-width:173px">
                                    <div class="mdc-button__ripple"></div>
                                    <span class="mdc-button__label">GeoValidate</span>
                                    <i class="icon-check icon-24" id="geovalid_address_selected" style="display:none"></i>
                                </button>
                                <div class="col s2"></div>
                                <div class="preloader-wrapper small active" id="preloader_container" style="display:none">
                                    <div class="spinner-layer spinner-blue-only">
                                        <div class="circle-clipper left">
                                            <div class="circle"></div>
                                        </div><div class="gap-patch">
                                            <div class="circle"></div>
                                        </div><div class="circle-clipper right">
                                            <div class="circle"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col s2 right-align">
                                <button disabled class="mdc-button btn More_Button" type="button" style="display:none">
                                    <div class="mdc-button__ripple"></div>
                                    <span class="mdc-button__label">More..</span>
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="responsive-table col s8" style="margin-top: 20px;">
                                <table class="highlight">
                                    <thead>
                                        <tr>
                                            <th class="center">
                                                <x>Geo Validated Addresses</x>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="verifiedLocationsList"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!--<div class="row">
                <div class="valign-wrapper" style="margin-left: 10px; color: #9FA3AB">
                    <h6><b>Complete the following to perform a Records Check</b></h6>
                </div>
            </div>
            <div class="row">
                <div class="input-field col s4" mandatory="true">
                    <input placeholder="" id="lastname" name="lastname" type="text" maxlength="20">
                    <label for="lastname" class="active">Last Name</label>
                </div>
                <div class="input-field col s4" mandatory="true">
                    <input placeholder="" id="firstname" name="firstname" type="text" maxlength="15">
                    <label for="firstname" class="active">First Name</label>
                </div>
            </div>
            <div class="row">
                <div class="input-field col s2" mandatory="true">
                    <input id="dob" name="dob" type="date" >
                    <label for="dob" class="active">Date of Birth</label>
                </div>
                <div class="input-field col s3" type="selectlist" id="racevals" name="racevals">
                    <label>Race</label>
                </div>
            </div>-->
            </div>
        </div>
        <div class="Flex-Form-Diverts" style="display:none;">
            <div id="divertLocationRow" class="row">
                <div id="divertLocationName" class="col"></div>
                <div id="proceedButton" class="waves-effect btn-flat col">
                    <span>PROCEED TO LOCATION</span>
                    <i class="icon-arrow_forward icon-18"></i>
                </div>
            </div>
            <div id="divertChipsRow">
                <div class="row">Current Active Divert Reasons</div>
                <div id="divertChipList" class="row"></div>
            </div>
            <div id="divertListRow"></div>
        </div>

        <div id="modal" class="modal modal-fixed-footer">
            <div class="header valign-wrapper modal-header warning">
                <h5>ADD TAG</h5>
            </div>
            <div class="modal-content">
                <div id="divert_modal_msg" class="row"></div>
                <div id="divert_modal_reasons" class="row"></div>
            </div>
            <div class="modal-footer">
                <a id="closebtn" class="modal-close btn-flat">CANCEL</a>
                <a id="addbtn" class="modal-close btn">CONFIRM + SUBMIT</a>
            </div>
        </div>

        <!--Hidden Inputs that are passed to the query server-->
        <input type="hidden" name="CallTaking_Performed_By" id="CallTaking_Performed_By">
        <input type="hidden" name="SelectedAddressTab" id="SelectedAddressTab" value="hospitalstab">
        <input type="hidden" name="SelectedAddressIndex" id="SelectedAddressIndex" value="-1">
        <input type="hidden" name="transportationwarning" id="transportationwarning" value="">
        <input type="hidden" name="location" id="location">
    </form>
</body>
</html>


